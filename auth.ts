import NextAuth from "next-auth"
import Facebook from "next-auth/providers/facebook"
import GitHub from "next-auth/providers/github"
import Google from "next-auth/providers/google"
import { insertData, getDataValue } from "./src/lib/supabase"

export const { auth, handlers } = NextAuth({
  providers: [
    GitHub({
      clientId: process.env.GITHUB_CLIENT_ID ?? "",
      clientSecret: process.env.GITHUB_CLIENT_SECRET ?? "",
    }),
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID ?? "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET ?? "",
    }),
    Facebook({
      clientId: process.env.FACEBOOK_CLIENT_ID ?? "",
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET ?? "",
    }),
  ],
  secret: process.env.NEXT_AUTH_SECRET,
  callbacks: {
    async signIn({ user }) {
      try {
        // Check if user already exists in profiles table
        if (user.email) {
          const existingData = await getDataValue('profiles', 'email', user.email, 'full_name');

          // If user doesn't exist, create profile entry
          if (existingData === null) {
            const { data, error } = await insertData('profiles', {
              full_name: user.name || '',
              email: user.email,
              avatar_url: user.image || ''
            });

            if (error) {
              console.error('Error creating user profile:', error);
            } else {
              console.log('User profile created successfully:', data);
            }
          }
        }
        return true;
      } catch (error) {
        console.error('Error in signIn callback:', error);
        return true; // Allow signin even if profile creation fails
      }
    },
  },
})
