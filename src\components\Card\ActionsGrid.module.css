.card {
  background-color: light-dark(var(--mantine-color-gray-0), var(--mantine-color-dark-6));
}

.title {
  font-family: Outfit, var(--mantine-font-family);
  font-weight: 500;
}

.item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: var(--mantine-radius-md);
  height: 90px;
  background-color: var(--mantine-color-body);
  transition:
    box-shadow 150ms ease,
    transform 100ms ease;

  &:hover {
    box-shadow: var(--mantine-shadow-md);
    transform: scale(1.05);
  }
}

.wideItem {
  width: 100%;
  height: 60px;
}
