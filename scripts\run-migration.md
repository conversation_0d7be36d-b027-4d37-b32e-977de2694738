# Database Migration Instructions

## Issue
The "Refresh Contact Data" functionality is failing because the database schema is missing the new `extra` column that was added for location coordinates.

## Error
```
Could not find the 'attributes' column of 'contact' in the schema cache
```

## Solution
You need to add the `extra` column to your existing database. Here are the steps:

### Option 1: Using Supabase Dashboard (Recommended)
1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `scripts/add-extra-column.sql`
4. Run the script

### Option 2: Using psql command line
If you have direct database access:
```bash
psql -h your-host -U your-user -d your-database -f scripts/add-extra-column.sql
```

### Option 3: Manual SQL
Run this SQL command in your database:
```sql
-- Add the extra column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'contact' 
        AND column_name = 'extra'
    ) THEN
        ALTER TABLE contact ADD COLUMN extra JSONB;
        
        -- Add index for the new column
        CREATE INDEX IF NOT EXISTS idx_contact_extra ON contact USING GIN (extra);
        
        -- Add comment for documentation
        COMMENT ON COLUMN contact.extra IS 'JSON object containing extra data: {"map": "coordinates", etc.}';
        
        RAISE NOTICE 'Added extra column to contact table';
    ELSE
        RAISE NOTICE 'Extra column already exists in contact table';
    END IF;
END $$;
```

## Verification
After running the migration, you can verify it worked by running:
```sql
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'contact' 
AND column_name = 'extra';
```

You should see:
```
column_name | data_type | is_nullable
extra       | jsonb     | YES
```

## What This Fixes
- ✅ "Refresh Contact Data" functionality will work again
- ✅ Location coordinates can be saved and retrieved
- ✅ Map icon will appear on profile pages when coordinates exist
- ✅ All existing functionality remains intact
