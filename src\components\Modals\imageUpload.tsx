"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import {
  Modal,
  Button,
  Group,
  Text,
  Box,
  Stack,
  Image,
  ActionIcon,
  Progress,
  Alert,
  SimpleGrid,
  Paper
} from '@mantine/core';
import { Dropzone, IMAGE_MIME_TYPE } from '@mantine/dropzone';
import { notifications } from '@mantine/notifications';
import { IconUpload, IconX, IconPhoto, IconTrash } from '@tabler/icons-react';
import { fetchData, updateData, uploadFile, deleteFile, getFileUrl } from '../../lib/supabase';

interface ImageUploadModalProps {
  opened: boolean;
  onClose: () => void;
  contactName: string;
  onContactUpdated?: () => void;
}

interface ImageData {
  img1?: string;
  img2?: string;
  img3?: string;
}

export function ImageUploadModal({ opened, onClose, contactName, onContactUpdated }: ImageUploadModalProps) {
  const { data: session } = useSession();
  const [images, setImages] = useState<ImageData>({});
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Fetch existing images when modal opens
  useEffect(() => {
    if (opened && contactName) {
      fetchExistingImages();
    }
  }, [opened, contactName]);

  const fetchExistingImages = async () => {
    try {
      setLoading(true);
      const { data, error } = await fetchData('contact', {
        select: 'images',
        filter: [{ column: 'name', value: contactName.toLowerCase() }],
        single: true
      });

      if (!error && data) {
        const imageData = (data as any).images;
        if (imageData) {
          // Handle both string JSON and object formats
          const parsedImages = typeof imageData === 'string'
            ? JSON.parse(imageData)
            : imageData;
          setImages(parsedImages || {});
        } else {
          setImages({});
        }
      }
    } catch (error) {
      console.error('Error fetching images:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to load existing images',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  // Resize image to 300px max width/height
  const resizeImage = (file: File): Promise<File> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      const img = new window.Image();

      img.onload = () => {
        const maxSize = 300;
        let { width, height } = img;

        // Calculate new dimensions
        if (width > height) {
          if (width > maxSize) {
            height = (height * maxSize) / width;
            width = maxSize;
          }
        } else {
          if (height > maxSize) {
            width = (width * maxSize) / height;
            height = maxSize;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and resize
        ctx.drawImage(img, 0, 0, width, height);

        canvas.toBlob((blob) => {
          if (blob) {
            const resizedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(resizedFile);
          }
        }, file.type, 0.8);
      };

      img.src = URL.createObjectURL(file);
    });
  };

  // Get next available image slot
  const getNextImageSlot = (): string | null => {
    for (let i = 1; i <= 3; i++) {
      const key = `img${i}` as keyof ImageData;
      if (!images[key]) {
        return key;
      }
    }
    return null;
  };

  // Handle file upload
  const handleFileUpload = async (files: File[]) => {
    if (files.length === 0) return;

    const file = files[0];
    const nextSlot = getNextImageSlot();

    if (!nextSlot) {
      notifications.show({
        title: 'Upload Limit Reached',
        message: 'You can only upload up to 3 images',
        color: 'orange',
      });
      return;
    }

    try {
      setUploading(true);
      setUploadProgress(0);

      // Resize image
      setUploadProgress(25);
      const resizedFile = await resizeImage(file);

      // Generate unique filename
      const timestamp = Date.now();
      const fileName = `${contactName}_${nextSlot}_${timestamp}.${file.name.split('.').pop()}`;
      const filePath = `profile-images/${fileName}`;

      setUploadProgress(50);

      // Upload to Supabase storage
      const { data: uploadData, error: uploadError } = await uploadFile(
        'images', // bucket name
        filePath,
        resizedFile
      );

      if (uploadError) throw uploadError;

      setUploadProgress(75);

      // Get public URL
      const imageUrl = getFileUrl('images', filePath);

      // Update images object
      const updatedImages = {
        ...images,
        [nextSlot]: imageUrl
      };

      // Update database
      const { error: updateError } = await updateData(
        'contact',
        { images: updatedImages },
        { column: 'name', value: contactName.toLowerCase() }
      );

      if (updateError) throw updateError;

      setUploadProgress(100);
      setImages(updatedImages);

      notifications.show({
        title: 'Success',
        message: 'Image uploaded successfully',
        color: 'green',
      });

      // Call the callback to refresh the parent component
      if (onContactUpdated) {
        onContactUpdated();
      }

    } catch (error) {
      console.error('Error uploading image:', error);
      notifications.show({
        title: 'Upload Failed',
        message: 'Failed to upload image. Please try again.',
        color: 'red',
      });
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  // Delete image
  const handleDeleteImage = async (imageKey: keyof ImageData) => {
    const imageUrl = images[imageKey];
    if (!imageUrl) return;

    try {
      setLoading(true);

      // Extract file path from URL
      const urlParts = imageUrl.split('/');
      const fileName = urlParts[urlParts.length - 1];
      const filePath = `profile-images/${fileName}`;

      // Delete from storage
      await deleteFile('images', filePath);

      // Update images object
      const updatedImages = { ...images };
      delete updatedImages[imageKey];

      // Update database
      const { error: updateError } = await updateData(
        'contact',
        { images: updatedImages },
        { column: 'name', value: contactName.toLowerCase() }
      );

      if (updateError) throw updateError;

      setImages(updatedImages);

      notifications.show({
        title: 'Success',
        message: 'Image deleted successfully',
        color: 'green',
      });

      // Call the callback to refresh the parent component
      if (onContactUpdated) {
        onContactUpdated();
      }

    } catch (error) {
      console.error('Error deleting image:', error);
      notifications.show({
        title: 'Delete Failed',
        message: 'Failed to delete image. Please try again.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const imageCount = Object.values(images).filter(Boolean).length;
  const canUpload = imageCount < 3;

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title="Upload Profile Images"
      size="lg"
      centered
    >
      <Stack gap="md">
        {/* Upload Area */}
        {canUpload && (
          <Dropzone
            onDrop={handleFileUpload}
            accept={IMAGE_MIME_TYPE}
            multiple={false}
            disabled={uploading || loading}
            style={{
              border: '2px dashed #e9ecef',
              borderRadius: '8px',
              padding: '20px',
              textAlign: 'center',
              cursor: uploading ? 'not-allowed' : 'pointer'
            }}
          >
            <Group justify="center" gap="xl" style={{ minHeight: 120, pointerEvents: 'none' }}>
              <Dropzone.Accept>
                <IconUpload size={50} color="green" />
              </Dropzone.Accept>
              <Dropzone.Reject>
                <IconX size={50} color="red" />
              </Dropzone.Reject>
              <Dropzone.Idle>
                <IconPhoto size={50} color="gray" />
              </Dropzone.Idle>

              <div>
                <Text size="xl" inline>
                  Drag images here or click to select files
                </Text>
                <Text size="sm" color="dimmed" inline mt={7}>
                  Only image files are accepted. Images will be resized to 300px for optimal storage.
                </Text>
                <Text size="sm" color="dimmed" inline mt={7}>
                  {imageCount}/3 images uploaded
                </Text>
              </div>
            </Group>
          </Dropzone>
        )}

        {/* Upload Progress */}
        {uploading && (
          <Box>
            <Text size="sm" mb="xs">Uploading image...</Text>
            <Progress value={uploadProgress} animated />
          </Box>
        )}

        {/* Image Limit Alert */}
        {!canUpload && (
          <Alert color="orange" title="Upload Limit Reached">
            You have reached the maximum limit of 3 images. Delete an existing image to upload a new one.
          </Alert>
        )}

        {/* Existing Images */}
        {imageCount > 0 && (
          <Box>
            <Text size="lg" fw={500} mb="md">Uploaded Images</Text>
            <SimpleGrid cols={3} spacing="md">
              {Object.entries(images).map(([key, url]) => {
                if (!url) return null;
                return (
                  <Paper key={key} p="xs" withBorder style={{ position: 'relative' }}>
                    <Image
                      src={url}
                      alt={`Profile image ${key}`}
                      height={100}
                      fit="cover"
                      radius="sm"
                    />
                    <ActionIcon
                      variant="filled"
                      color="red"
                      size="sm"
                      style={{
                        position: 'absolute',
                        top: 5,
                        right: 5,
                        backgroundColor: '#ff4757',
                        border: '2px solid #ff3742',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.3)'
                      }}
                      onClick={() => handleDeleteImage(key as keyof ImageData)}
                      disabled={loading}
                    >
                      <IconTrash size={14} color="white" />
                    </ActionIcon>
                    <Text size="xs" ta="center" mt="xs" color="dimmed">
                      {key}
                    </Text>
                  </Paper>
                );
              })}
            </SimpleGrid>
          </Box>
        )}

        {/* Actions */}
        <Group justify="flex-end" mt="md">
          <Button variant="outline" onClick={onClose} disabled={uploading}>
            Close
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}
