'use client';
import { <PERSON><PERSON>, Text, But<PERSON>, Stack, Group, SemiCircleProgress, Alert, List, ThemeIcon } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useState, useEffect } from 'react';
import { IconCheck, IconX, IconAlertCircle } from '@tabler/icons-react';
import { updateData } from 'src/lib/supabase';
import { ImageData } from 'src/lib/common';
import { VIEW_PROFILE_URL } from 'src/lib/config';

interface UserInfo {
  profile: string;
  email: string;
  description?: string;
  images?: ImageData;
  twitter?: string;
  telegram?: string;
  youtube?: string;
  instagram?: string;
  facebook?: string;
  web2?: string;
  web3?: string;
}

interface MintNftModalProps {
  opened: boolean;
  onClose: () => void;
  contactName: string;
  userInfo: UserInfo | null;
  onNftMinted?: () => void;
}

interface RewardProgress {
  description: boolean;
  email: boolean;
  socialMedia: boolean;
  images: boolean;
}

export function MintNftModal({ opened, onClose, contactName, userInfo, onNftMinted }: MintNftModalProps) {
  const [progress, setProgress] = useState<RewardProgress>({
    description: false,
    email: false,
    socialMedia: false,
    images: false
  });
  const [progressPercentage, setProgressPercentage] = useState(0);
  const [isMinting, setIsMinting] = useState(false);
  const [mintingProgress, setMintingProgress] = useState(0);

  // Check reward completion status
  useEffect(() => {
    if (!userInfo) return;

    const newProgress: RewardProgress = {
      description: !!(userInfo.description && userInfo.description.trim() !== ''),
      email: !!(userInfo.email && userInfo.email.trim() !== ''),
      socialMedia: !!(
        (userInfo.twitter && userInfo.twitter.trim() !== '') ||
        (userInfo.telegram && userInfo.telegram.trim() !== '') ||
        (userInfo.youtube && userInfo.youtube.trim() !== '') ||
        (userInfo.instagram && userInfo.instagram.trim() !== '') ||
        (userInfo.facebook && userInfo.facebook.trim() !== '') ||
        (userInfo.web2 && userInfo.web2.trim() !== '') ||
        (userInfo.web3 && userInfo.web3.trim() !== '')
      ),
      images: !!(userInfo.images && Object.keys(userInfo.images).length > 0)
    };

    setProgress(newProgress);

    // Calculate percentage
    const completedCount = Object.values(newProgress).filter(Boolean).length;
    const totalCount = Object.keys(newProgress).length;
    const percentage = Math.round((completedCount / totalCount) * 100);
    setProgressPercentage(percentage);
  }, [userInfo]);

  const handleMintNft = async () => {
    if (progressPercentage < 100) {
      notifications.show({
        title: 'Requirements Not Met',
        message: 'Please complete all required fields before minting NFT',
        color: 'orange',
      });
      return;
    }

    setIsMinting(true);
    setMintingProgress(0);

    try {
      // Parse the contact name to extract domain and primary
      const nameParts = contactName.split('@');
      if (nameParts.length !== 2) {
        throw new Error('Invalid contact name format. Expected format: domain@primary');
      }

      const [domain, primary] = nameParts;

      // Simulate progress
      setMintingProgress(25);

      // Make API call to remote endpoint
      const apiUrl = `https://api.odude.com/studio_qr.php?domain=${encodeURIComponent(domain)}&primary=${encodeURIComponent(primary)}&key=0000`;

      setMintingProgress(50);

      const response = await fetch(apiUrl);

      setMintingProgress(75);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.status === 'success' && result.url) {
        // Update the image and web2 fields in the contact table
        const web2Url = VIEW_PROFILE_URL + contactName.toLowerCase();
        const updateResult = await updateData(
          'contact',
          { image: result.url, web2: web2Url },
          { column: 'name', value: contactName.toLowerCase() }
        );

        if (updateResult.error) {
          throw updateResult.error;
        }

        setMintingProgress(100);

        notifications.show({
          title: 'NFT Minted Successfully!',
          message: 'Your NFT has been created and saved to your profile',
          color: 'green',
        });

        // Call the callback to refresh the parent component
        if (onNftMinted) {
          onNftMinted();
        }

        // Close modal after a short delay
        setTimeout(() => {
          onClose();
        }, 2000);

      } else {
        throw new Error('Server busy, try later');
      }

    } catch (error) {
      console.error('Error minting NFT:', error);
      notifications.show({
        title: 'Minting Failed',
        message: error instanceof Error ? error.message : 'Server busy, try later',
        color: 'red',
      });
    } finally {
      setIsMinting(false);
      setMintingProgress(0);
    }
  };

  const getRequirementIcon = (completed: boolean) => {
    return completed ? (
      <ThemeIcon color="green" size={24} radius="xl">
        <IconCheck size={16} />
      </ThemeIcon>
    ) : (
      <ThemeIcon color="red" size={24} radius="xl">
        <IconX size={16} />
      </ThemeIcon>
    );
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={`Apply NFT for ${contactName}`}
      centered
      size="md"
    >
      <Stack gap="md">
        <Text size="sm" c="dimmed">
          Complete all requirements to earn your NFT reward
        </Text>

        {/* Warning Message */}
        <Alert icon={<IconAlertCircle size={12} />} title="Important Notice" color="orange">
          <Text size="xs">
            Once you apply for NFT, you will <strong>not be able to rename</strong> this ODude Name ({contactName}).
            Please make sure before proceeding.
          </Text>
        </Alert>

        {/* Progress Circle */}
        <Group justify="center">
          <SemiCircleProgress
            fillDirection="left-to-right"
            orientation="up"
            filledSegmentColor={progressPercentage === 100 ? "green" : "blue"}
            size={150}
            thickness={8}
            value={progressPercentage}
            label={`${progressPercentage}%`}
          />
        </Group>

        {/* Requirements List */}
        <List spacing="xs" size="sm">
          <List.Item icon={getRequirementIcon(progress.description)}>
            <Text size="sm">
              Description field completed
            </Text>
          </List.Item>

          <List.Item icon={getRequirementIcon(progress.email)}>
            <Text size="sm">
              Email address provided
            </Text>
          </List.Item>

          <List.Item icon={getRequirementIcon(progress.socialMedia)}>
            <Text size="sm">
              At least one social media field completed
            </Text>
          </List.Item>

          <List.Item icon={getRequirementIcon(progress.images)}>
            <Text size="sm">
              At least one image uploaded
            </Text>
          </List.Item>
        </List>

        {/* Minting Progress */}
        {isMinting && (
          <Alert icon={<IconAlertCircle size={16} />} title="Minting in Progress" color="blue">
            <Text size="sm">Please wait while we create your NFT...</Text>
            <Text size="xs" mt="xs">Progress: {mintingProgress}%</Text>
          </Alert>
        )}

        {/* Action Buttons */}
        <Group justify="flex-end" mt="md">
          <Button variant="outline" onClick={onClose} disabled={isMinting}>
            Cancel
          </Button>

          {progressPercentage === 100 ? (
            <Button
              onClick={handleMintNft}
              loading={isMinting}
              color="green"
            >
              Mint Now
            </Button>
          ) : (
            <Button disabled>
              Complete Requirements First
            </Button>
          )}
        </Group>
      </Stack>
    </Modal>
  );
}
