"use client"

import { FacebookButton } from "src/components/Buttons/FacebookButton"
import { GoogleButton } from "src/components/Buttons/GoogleButton"
import { Title, Stack, Container, Box } from "@mantine/core"

import styles from "./index.module.scss"

import { useSession } from "next-auth/react";

export const SignIn = () => {

  return (
    <Container
      className={styles.signin_container}
      p="3rem"
      h="10vh"
      w="100%"
      display="flex"
      style={{
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      <Box ta="center" w="100%">
        <Title
          order={1}
          ta="center"
          mb="md"
        >
          Sign in with your account
        </Title>
        <Stack className={styles.btn_group} align="center">
          <GoogleButton />
        </Stack>
      </Box>
    </Container>
  )
}
