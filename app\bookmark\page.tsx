"use client"

import { Card } from "src/components/Card"
import { Page } from "src/components/Page"
import { UserBio } from "src/components/Card/bio"
import { useAuthRedirect } from "src/hooks/useAuthRedirect"
import { useEffect, useState, useRef, useCallback } from "react"
import { fetchData, FilterOperator } from "src/lib/supabase"
import { Button, Text, Loader, TextInput, Group, Paper, Title, Divider, Grid } from "@mantine/core"
import { IconSearch } from "@tabler/icons-react"

interface BookmarkItem {
  id: string;
  contact_name: string;
  contact_email: string;
  created_at: string;
}

export default function BookmarkPage() {
  const { session, isLoading } = useAuthRedirect();
  const [bookmarks, setBookmarks] = useState<BookmarkItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(0);
  const observer = useRef<IntersectionObserver | null>(null);
  const ITEMS_PER_PAGE = 10;

  const fetchBookmarks = useCallback(async (pageNum: number, query: string = "") => {
    if (!session?.user?.email) return;

    try {
      setLoading(true);
      const { data, error } = await fetchData<BookmarkItem[]>('bookmark', {
        select: 'id, contact_name, contact_email, created_at',
        filter: [
          { column: 'contact_email', value: session.user.email },
          ...(query ? [{ column: 'contact_name', operator: 'ilike' as FilterOperator, value: `%${query}%` }] : [])
        ],
        order: { column: 'created_at', ascending: false },
        limit: ITEMS_PER_PAGE
      });

      if (error) {
        setError('Failed to fetch bookmarks');
        return;
      }

      if (data) {
        if (pageNum === 0) {
          setBookmarks(data as BookmarkItem[]);
        } else {
          setBookmarks(prev => [...prev, ...(data as BookmarkItem[])]);
        }
        setHasMore((data as BookmarkItem[]).length === ITEMS_PER_PAGE);
      }
    } catch (err) {
      setError('An unexpected error occurred');
      console.error(err);
    } finally {
      setLoading(false);
    }
  }, [session]);

  const lastBookmarkElementRef = useCallback((node: HTMLDivElement | null) => {
    if (loading) return;
    if (observer.current) observer.current.disconnect();

    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        setPage(prevPage => prevPage + 1);
      }
    });

    if (node) observer.current.observe(node);
  }, [loading, hasMore]);

  useEffect(() => {
    if (session?.user?.email) {
      fetchBookmarks(0, searchQuery);
    }
  }, [session, searchQuery]);

  useEffect(() => {
    if (page > 0) {
      fetchBookmarks(page, searchQuery);
    }
  }, [page]);

  const handleSearch = () => {
    setPage(0);
    fetchBookmarks(0, searchQuery);
  };

  // Show loading while authentication is being checked
  if (isLoading) {
    return (
      <Page>
        <Card>
          <div style={{ display: 'flex', justifyContent: 'center', padding: '1rem' }}>
            <Loader />
          </div>
        </Card>
      </Page>
    );
  }

  // If not authenticated, the useAuthRedirect hook will handle the redirect
  if (!session) {
    return null;
  }

  return (
    <Page>
      <Card>
        
        <Divider id="div-label" my="xs" label="Your Bookmarks" labelPosition="center" w="100%" />

        <Group justify="center" mb="xl">
          <TextInput
            placeholder="Search bookmarks..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
            leftSection={<IconSearch size={16} />}
            style={{ width: '80%' }}
          />
          <Button onClick={handleSearch}>Search</Button>
        </Group>

        {error && (
          <Text c="red" ta="center" mb="md">
            {error}
          </Text>
        )}

        {bookmarks.length === 0 && !loading ? (
          <Text ta="center" c="dimmed">
            No bookmarks found
          </Text>
        ) : (
          <Grid>
            {bookmarks.map((bookmark, index) => {
              const isLastElement = index === bookmarks.length - 1;
              return (
                <Grid.Col key={bookmark.id} span={{ base: 12, sm: 6 }} ref={isLastElement ? lastBookmarkElementRef : null}>
              
                    <UserBio name={bookmark.contact_name} />
                    
              
                </Grid.Col>
              );
            })}
          </Grid>
        )}

        {loading && (
          <div style={{ display: 'flex', justifyContent: 'center', padding: '1rem' }}>
            <Loader />
          </div>
        )}
      </Card>
    </Page>
  )
}