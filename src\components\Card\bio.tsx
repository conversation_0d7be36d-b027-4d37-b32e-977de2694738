'use client';
import { IconAt, IconPhoneCall, IconChevronRight } from '@tabler/icons-react';
import { Avatar, Group, Text, UnstyledButton } from '@mantine/core';
import classes from './bio.module.css';
import { useState, useEffect } from 'react';
import { fetchData } from 'src/lib/supabase';
import { useRouter } from "next/navigation";
import { getFirstImage, ImageData } from 'src/lib/common';
import { cleanupBookmarksForNonExistentContact } from 'src/lib/bookmark';

interface UserInfo {
  profile: string;
  email: string;
  phone?: string;
  image?: string;
  description?: string;
  images?: ImageData;
}

export function UserBio({ name }: { name: string }) {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const { data } = await fetchData<UserInfo>('contact', {
          select: 'profile, email, phone, description, images',
          filter: [{ column: 'name', value: name.toLowerCase() }],
          single: true
        });
       // console.log('User data from Supabase:', data);
        const userData = Array.isArray(data) ? data[0] : data;
        setUserInfo(userData);

        // If user is not found, clean up any existing bookmarks
        if (!userData) {
          await cleanupBookmarksForNonExistentContact(name.toLowerCase());
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        // Also clean up bookmarks on error (user likely doesn't exist)
        await cleanupBookmarksForNonExistentContact(name.toLowerCase());
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [name]);

  if (loading) return <div>Loading...</div>;
  if (!userInfo) return <div>User not found : {name}</div>;

  return (
    <div>
      <UnstyledButton
        className={classes.user}
        onClick={() => router.push(`/profile/${name.toLowerCase()}`)}
        style={{ width: '100%' }}
      >
        <Group wrap="nowrap">
          <Avatar
            src={getFirstImage(userInfo.images)}
            size={94}
            radius="md"
          />
          <div style={{ flex: 1 }}>


            <Text fz="lg" fw={500} className={classes.name}>
              {userInfo.profile}
            </Text>

            <Group wrap="nowrap" gap={10} mt={3}>
              <IconAt stroke={1.5} size={16} className={classes.icon} />
              <Text fz="xs" c="dimmed">
                {name}
              </Text>
            </Group>

            {userInfo.phone && (
              <Group wrap="nowrap" gap={10} mt={5}>
                <IconPhoneCall stroke={1.5} size={16} className={classes.icon} />
                <Text fz="xs" c="dimmed">
                  {userInfo.phone}
                </Text>
              </Group>
            )}
          </div>
          <IconChevronRight size={14} stroke={1.5} />
        </Group>
      </UnstyledButton>
    </div>
  );
}