import type { <PERSON>ada<PERSON> } from "next"
import { auth } from "auth"
import { SessionProvider } from "next-auth/react"
import { Inter } from "next/font/google"
import { MantineProvider } from "src/providers/MantineProvider"
import { ThemeToggle } from "src/components/Buttons/ThemeToggleButton"
import { HomeButton } from "src/components/Buttons/HomeButton"
import "@mantine/core/styles.css"
import "@mantine/notifications/styles.css"
import "styles/globals.scss"
import { Flex } from "@mantine/core"
import { ODudeNameSearch } from "src/components/Search"
import { UserMenu } from "src/components/Buttons/UserMenu"
import { Footer } from "src/components/Footer"
import { FooterProvider } from "src/providers/FooterProvider"


const inter = Inter({
  subsets: ["latin"],
  fallback: ["Helvetica", "sans-serif"],
})

export const metadata: Metadata = {
  title: "ODude Name",
  description: "Decentralized Name Service",
  icons: "/favicon.ico",
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const session = await auth()

  return (
    <html lang="en" className={inter.className}>
      <body>
        <SessionProvider session={session}>
          <MantineProvider>
            <FooterProvider>
              <Flex
                mih={50}
                bg="rgba(0, 0, 0, .3)"
                gap="md"
                justify="center"
                align="center"
                direction="row"
                wrap="wrap"
                mb={21}
              >
                <HomeButton />
                <ThemeToggle />
                <ODudeNameSearch />
                {session && (


                  <UserMenu />

                )}
              </Flex>
              {children}
              <Footer />
            </FooterProvider>
          </MantineProvider>
        </SessionProvider>
      </body>
    </html>
  )
}
