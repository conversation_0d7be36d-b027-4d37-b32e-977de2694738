'use client';

import { Flex } from "@mantine/core";
import { useFooter } from "src/providers/FooterProvider";

export const Footer = () => {
  const { footerContent } = useFooter();

  // Don't render footer if there's no content
  if (!footerContent) {
    return null;
  }

  return (
    <Flex
      mih={50}
      bg="rgba(0, 0, 0, .3)"
      gap="md"
      justify="center"
      align="center"
      direction="row"
      wrap="wrap"
      style={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 100
      }}
    >
      {footerContent}
    </Flex>
  );
};
