# Social Media & Crypto Address Structure Guide

This guide explains the new JSON-based structure for social media handles and cryptocurrency addresses.

## Overview

Social media handles and crypto addresses are now stored in JSON columns:
- Social: `social` (JSONB column)
- Crypto: `crypto` (JSONB column)

**Note**: This is a fresh start implementation. Old individual columns are no longer supported.

## Benefits

1. **Easy to add new platforms**: Just add them to the config file
2. **No database schema changes**: New social platforms or crypto currencies don't require ALTER TABLE
3. **Cleaner code**: Dynamic rendering based on configuration
4. **Better maintainability**: Centralized configuration

## Configuration

Social media platforms and crypto currencies are now configured in `src/lib/config.ts`:

```typescript
// Social Media Configuration
export const SOCIAL_SLOTS = ["twitter", "telegram", "youtube", "instagram", "facebook", "discord"];

// Cryptocurrency Configuration  
export const CRYPTO_SLOTS = ["eth", "bsc", "matic", "btc", "fil", "sol"];
```

To add a new platform (e.g., Discord), simply add it to the appropriate array.

## Database Setup

### Fresh Database Setup

For new installations, use the fresh database setup script:

```bash
# Run the fresh setup script on your Supabase database
psql -h your-host -U your-user -d your-database -f scripts/fresh-database-setup.sql
```

This creates the contact table with only the new JSON structure.

## Code Changes

### Database Types

Updated `src/lib/database.types.ts` to include new columns:
```typescript
social: Json | null
crypto: Json | null
```

### Utility Functions

New utility functions in `src/lib/database-utils.ts`:
- `getSocialData()` - Get social data from either format
- `getCryptoData()` - Get crypto data from either format
- `migrateSocialData()` - Convert legacy to new format
- `migrateCryptoData()` - Convert legacy to new format
- `prepareContactData()` - Prepare data for database operations

### Component Updates

All components now use the new utility functions:
- `app/profile/[id]/page.tsx` - Dynamic social/crypto rendering
- `app/update/page.tsx` - Dynamic form fields
- `src/components/Buttons/AddToMobileContactButton.tsx` - Updated vCard generation
- `app/api/[name]/route.ts` - Updated API response

## Adding New Platforms

### Example: Adding Discord

1. **Update config**:
```typescript
export const SOCIAL_SLOTS = ["twitter", "telegram", "youtube", "instagram", "facebook", "discord"];
```

2. **Add icon support** (in components that need it):
```typescript
case 'discord':
  return <IconBrandDiscord size={30} />;
```

3. **Add validation** (optional):
```typescript
case 'discord':
  // Add Discord-specific validation
  break;
```

That's it! The platform will automatically appear in forms and displays.

## Data Format

### New JSON Structure

```json
{
  "social": {
    "twitter": "username",
    "telegram": "username",
    "discord": "username#1234"
  },
  "crypto": {
    "eth": "0x1234...",
    "btc": "**********************************"
  }
}
```

### Database Schema

The contact table now includes:
```sql
social JSONB,  -- Social media handles
crypto JSONB   -- Cryptocurrency addresses
```

## Testing

1. **Test new entries**: Create new contacts and verify they use the new format
2. **Test updates**: Update existing contacts and verify data persistence
3. **Test new platforms**: Add a new platform to config and test
4. **Test validation**: Verify social and crypto validation works correctly

## Support

If you encounter issues:

1. Check that utility functions are imported correctly
2. Verify configuration in `src/lib/config.ts`
3. Check browser console for any errors
4. Verify database schema matches the fresh setup script

## Performance Notes

- JSON columns are indexed with GIN indexes for better performance
- Utility functions cache results where possible
- No performance impact on existing queries
