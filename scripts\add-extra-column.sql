-- Migration script to add extra column to contact table
-- This script adds the extra JSONB column for storing location coordinates and other extra data

-- Add the extra column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'contact' 
        AND column_name = 'extra'
    ) THEN
        ALTER TABLE contact ADD COLUMN extra JSONB;
        
        -- Add index for the new column
        CREATE INDEX IF NOT EXISTS idx_contact_extra ON contact USING GIN (extra);
        
        -- Add comment for documentation
        COMMENT ON COLUMN contact.extra IS 'JSON object containing extra data: {"map": "coordinates", etc.}';
        
        RAISE NOTICE 'Added extra column to contact table';
    ELSE
        RAISE NOTICE 'Extra column already exists in contact table';
    END IF;
END $$;

-- Verify the column was added
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'contact' 
AND column_name = 'extra';
